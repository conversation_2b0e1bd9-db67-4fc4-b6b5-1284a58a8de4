import { Image } from "@heroui/image";
import { Button } from "@heroui/button";
import { useStore } from "@/store";
import { Input } from "@heroui/input";
import { NetworkSelector } from "@/components/NetworkSelector";
import { networks } from "@/config/network.ts";

export default function Bridge() {
  const { lang } = useStore();

  return (
    <div className="w-full">
      <div className="bg-color6 border-border rounded-xl w-full px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="font-normal text-lg text-color7 flex-1">
            From Network
          </div>
          <div className="w-20" />
          <div className="font-normal text-lg text-color7 flex-1">
            Destination Network
          </div>
        </div>

        {/*<div className="w-full flex items-center mt-3">*/}
        {/*  <Button className="bg-color3 rounded-full flex items-center justify-between px-3 h-12 flex-1">*/}
        {/*    <div className="flex items-center">*/}
        {/*      <Image className="size-6" src="/public/images/chain_iotex.svg" />*/}
        {/*      <div className="text-lg text-color8 ml-2">IoTeX</div>*/}
        {/*    </div>*/}
        {/*    <Image className="size-6" src="/public/images/icon_down.svg" />*/}
        {/*  </Button>*/}
        {/*  <Image*/}
        {/*    className="size-12 mx-4 cursor-pointer"*/}
        {/*    src="/public/images/icon_arrow.svg"*/}
        {/*  />*/}
        {/*  <Button className="bg-color3 rounded-full flex items-center justify-between px-3 h-12 flex-1">*/}
        {/*    <div className="flex items-center">*/}
        {/*      <Image className="size-6" src="/public/images/chain_iotex.svg" />*/}
        {/*      <div className="text-lg text-color8 ml-2">IoTeX</div>*/}
        {/*    </div>*/}
        {/*    <Image className="size-6" src="/public/images/icon_down.svg" />*/}
        {/*  </Button>*/}
        {/*</div>*/}

        <NetworkSelector
          className="w-full flex items-center mt-3"
          networks={networks}
        >
          <Button className="bg-color3 rounded-full flex items-center justify-between px-3 h-12 flex-1">
            <div className="flex items-center">
              <Image className="size-6" src="/public/images/chain_iotex.svg" />
              <div className="text-lg text-color8 ml-2">IoTeX</div>
            </div>
            <Image className="size-6" src="/public/images/icon_down.svg" />
          </Button>
          <Image
            className="size-12 mx-4 cursor-pointer"
            src="/public/images/icon_arrow.svg"
          />
          <Button className="bg-color3 rounded-full flex items-center justify-between px-3 h-12 flex-1">
            <div className="flex items-center">
              <Image className="size-6" src="/public/images/chain_iotex.svg" />
              <div className="text-lg text-color8 ml-2">IoTeX</div>
            </div>
            <Image className="size-6" src="/public/images/icon_down.svg" />
          </Button>
        </NetworkSelector>

        <div className="flex items-center justify-between mt-6">
          <div className="text-lg text-color7">Select Token</div>
          <div className="text-sm text-color7">Balance: 300 IOTX</div>
        </div>

        <div className="w-full flex items-center mt-3">
          <Button className="bg-color3 rounded-full flex items-center justify-between px-3 h-12 flex-1">
            <div className="flex items-center">
              <Image className="size-5" src="/public/images/chain_iotex.svg" />
              <div className="text-lg text-color8 ml-2">IOTX</div>
            </div>
            <Image className="size-6" src="/public/images/icon_down.svg" />
          </Button>
          <div className="w-6" />
          <div className="px-4 border-1 border-color9 box-border rounded-full flex items-center justify-between flex-1 h-12">
            <Input
              className="mr-2 flex-1"
              classNames={{
                input: [
                  "bg-transparent",
                  "text-color8",
                  "placeholder:text-color7 dark:placeholder:text-white/60",
                  "text-lg",
                ],
                innerWrapper: "bg-transparent",
                inputWrapper: [
                  "bg-transparent",
                  "dark:transparent",
                  "hover:bg-transparent",
                  "dark:hover:bg-transparent",
                  "group-data-[focus=true]:bg-transparent",
                  "!cursor-text",
                ],
              }}
              pattern="([1-9]\d*(\.\d+)?|0\.\d*[1-9]\d*)"
              placeholder="0"
            />
            <Button className="text-purple1 text-lg p-0 bg-transparent min-w-0">
              MAX
            </Button>
          </div>
        </div>

        <div className="mt-6 text-lg text-color7">Recipient Address</div>
        <div className="mt-3 border-1 border-color9 rounded-full h-12 flex items-center justify-between p-4 gap-2">
          <Input
            className="mr-2 flex-1"
            classNames={{
              input: [
                "bg-transparent",
                "text-color8",
                "placeholder:text-color7 dark:placeholder:text-white/60",
                "text-lg",
              ],
              innerWrapper: "bg-transparent",
              inputWrapper: [
                "bg-transparent",
                "dark:transparent",
                "hover:bg-transparent",
                "dark:hover:bg-transparent",
                "group-data-[focus=true]:bg-transparent",
                "!cursor-text",
              ],
            }}
            placeholder="0x"
          />
          <Button className="text-purple1 text-lg p-0 bg-transparent">
            My Wallet
          </Button>
        </div>

        <div className="mt-6 text-lg text-color7">Get on Ethereum</div>
        <div className="mt-2 flex items-center">
          <Image className="size-10" src="/public/images/chain_iotex.svg" />
          <div className="ml-3">
            <div className="text-color8 text-2xl">99.99 IOTX</div>
            <div className="text-color7 text-xs">$2.156</div>
          </div>
        </div>

        <div className="flex items-center justify-between mt-4">
          <div className="flex items-center">
            <Image className="size-4" src="/public/images/icon_coin.svg" />
            <div className="text-xs text-color7 ml-2">
              Relay Fee: 0.00045ETH
            </div>
            <Image
              className="size-4 ml-4"
              src="/public/images/icon_exchange.svg"
            />
            <div className="text-xs text-color7 ml-2">Tube fee: 0</div>
          </div>
          <div className="flex items-center">
            <Image className="size-4" src="/public/images/icon_hourglass.svg" />
            <div className="text-xs text-color7 ml-2">About 5 mins</div>
          </div>
        </div>
      </div>

      <Button className="w-full rounded-full mt-8 h-12 font-bold text-xl bg-gradient-to-b from-purple3 to-purple4">
        Deposit
      </Button>

      <div className="text-color7 text-xs mt-4 text-center">
        {lang.t("description")}
      </div>
    </div>
  );
}
