import {
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger,
} from "@heroui/dropdown";
import { Network } from "@/types/networks.ts";
import { Image } from "@heroui/image";
import { ReactNode } from "react";
import { cn } from "@/lib/utils/util.ts";

export type NetworkSelectorProps = {
  children?: ReactNode;
  networks: Network[];
  className?: string;
};

export function NetworkSelector(props: NetworkSelectorProps) {
  return (
    <Dropdown className={cn(props.className)}>
      <DropdownTrigger>{props.children}</DropdownTrigger>
      <DropdownMenu
        disallowEmptySelection
        aria-label="Multiple selection example"
        closeOnSelect={false}
        selectionMode="single"
        variant="flat"
      >
        {props.networks.map((network) => (
          <DropdownItem key={network.chainId}>
            return (
            <Image
              alt={network.name}
              height={200}
              src={network.logoUrl}
              width={300}
            />
            <div>{network.name}</div>
          </DropdownItem>
        ))}
      </DropdownMenu>
    </Dropdown>
  );
}
