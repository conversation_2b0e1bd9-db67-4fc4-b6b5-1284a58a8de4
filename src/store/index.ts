import React from "react";
// import { <PERSON>uffer } from "buffer";
// (globalThis as any).Buffer = Buffer;

import { LangStore } from "@/store/lang";
import { TokenStore } from "@/store/token";
import { HistoryStore } from "@/store/history";
import { DepositStore } from "@/store/deposit";
// import { SolanaStore } from "@/store/solana";
// import { ETHLedger } from "@/store/ethLedger";

export class RootStore {
  // base = new BaseStore();
  lang = new LangStore();
  // god = new GodStore(this);
  token = new TokenStore(this);
  history = new HistoryStore();
  deposit = new DepositStore(this);
  // ledger = new ETHLedger(this);
  // solana = new SolanaStore(this);
}

export const rootStore = new RootStore();

const StoresContext = React.createContext(rootStore);

export const useStore = () => React.useContext(StoresContext);

//@ts-ignore
window._store = rootStore;
