import { makeAutoObservable } from "mobx";

export abstract class Network {
  name: string = "";
  fullName: string = "";
  chainId: number = 0;
  logoUrl: string = "";
  rpcUrl: string = "";
  explorerName: string = "";
  explorerURL: string = "";

  constructor(config: {
    name: string;
    fullName: string;
    chainId: number;
    logoUrl: string;
    rpcUrl: string;
    explorerName: string;
    explorerURL: string;
  }) {
    this.name = config.name;
    this.fullName = config.fullName;
    this.chainId = config.chainId;
    this.logoUrl = config.logoUrl;
    this.rpcUrl = config.rpcUrl;
    this.explorerURL = config.explorerURL;
    this.explorerName = config.explorerName;
    makeAutoObservable(this);
  }

  abstract isAddressValid(address: string): boolean;
}
