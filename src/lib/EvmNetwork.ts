import { Network } from "@/types/networks.ts";
import { isAddress } from "viem";

export class EvmNetwork extends Network {
  constructor(config: {
    name: string;
    fullName: string;
    chainId: number;
    logoUrl: string;
    rpcUrl: string;
    explorerURL: string;
    explorerName: string;
    gasPrice?: string;
    gasLimit?: string;
    [key: string]: any;
  }) {
    super(config);
  }

  isAddressValid(address: string): boolean {
    return isAddress(address);
  }
}