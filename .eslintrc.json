{"$schema": "https://json.schemastore.org/eslintrc.json", "env": {"browser": false, "es2021": true, "node": true}, "extends": ["plugin:react/recommended", "plugin:prettier/recommended", "plugin:react-hooks/recommended", "plugin:jsx-a11y/recommended"], "plugins": ["react", "unused-imports", "@typescript-eslint", "jsx-a11y"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 12, "sourceType": "module"}, "settings": {"react": {"version": "detect"}}, "rules": {"no-console": "warn", "react/prop-types": "off", "react/jsx-uses-react": "off", "react/react-in-jsx-scope": "off", "react-hooks/exhaustive-deps": "off", "jsx-a11y/click-events-have-key-events": "warn", "jsx-a11y/interactive-supports-focus": "warn", "prettier/prettier": "warn", "no-unused-vars": "off", "unused-imports/no-unused-vars": "off", "unused-imports/no-unused-imports": "warn", "@typescript-eslint/no-unused-vars": ["warn", {"args": "after-used", "ignoreRestSiblings": false, "argsIgnorePattern": "^_.*?$"}], "react/self-closing-comp": "warn", "react/jsx-sort-props": ["warn", {"callbacksLast": true, "shorthandFirst": true, "noSortAlphabetically": false, "reservedFirst": true}]}}